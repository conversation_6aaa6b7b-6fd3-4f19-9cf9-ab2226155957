using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Configuration.UserSecrets;
using SymXchangeAccountWSDL;
using System.ServiceModel;
using System.Threading.Tasks;

namespace MVCApp.Helpers
{
    public class SymXchangeAccountHelper
    {
        private readonly IConfiguration _config;
        private AccountServiceClient _accountClient;
        private DeviceInformation _device;
        private string _adminPassword;
        private BasicHttpsBinding _basicHttpsBinding;
        private CredentialsChoice _credChoice;

        public SymXchangeAccountHelper(IConfiguration config)
        {
            _config = config;
            var deviceNumber = _config.GetValue<short>("DeviceNumber");
            var deviceType = _config.GetValue<string>("DeviceType");
            _adminPassword = _config.GetValue<string>("SymXAdminPassword");

            _device = new DeviceInformation()
            {
                DeviceNumber = deviceNumber,
                DeviceType = deviceType
            };

            _basicHttpsBinding = new BasicHttpsBinding()
            {
                MaxBufferSize = **********,
                MaxBufferPoolSize = 524288,
                MaxReceivedMessageSize = **********
            };

            _credChoice = new CredentialsChoice()
            {
                Item = new AdministrativeCredentials()
                {
                    Password = _adminPassword
                }
            };
        }

        public async Task<updateShareByIDResponse> AddOdpToShare(string accountNumber, string shareID, short userId)
        {
            var endpointAddress = _config.GetValue<string>("AccountWSDLEndpoint");

            var endpoint = new EndpointAddress(endpointAddress);

            _accountClient = new AccountServiceClient(_basicHttpsBinding, endpoint);
            _credChoice.ProcessorUser = userId;
            _credChoice.ProcessorUserSpecified = true;

            var request = new UpdateShareByIDRequest()
            {
                Credentials = _credChoice,
                DeviceInformation = _device,
                MessageId = "UpdateODPRequest",
                BranchIdSpecified = false,
                AccountNumber = accountNumber,
                ShareId = shareID,
                ShareUpdateableFields = new ShareUpdateableFields()
                {
                    OverdraftTolerance = 700.00M,
                    OverdraftToleranceSpecified = true
                }
            };

            return await _accountClient.updateShareByIDAsync(request);
        }

        public async Task<createLoanNoteResponse> CreateLoanNote(string accountNumber, string loanID, short userId)
        {
            var endpointAddress = _config.GetValue<string>("AccountWSDLEndpoint");

            var endpoint = new EndpointAddress(endpointAddress);

            _accountClient = new AccountServiceClient(_basicHttpsBinding, endpoint);
            _credChoice.ProcessorUser = userId;
            _credChoice.ProcessorUserSpecified = true;

            var loanNote = new LoanNoteCreatableFields();
            if (loanNote.Text == null)
            {
                loanNote.Text = new LoanNoteUpdateableFieldsText[1];
            }
            loanNote.Text[0] = new LoanNoteUpdateableFieldsText();
            loanNote.Text[0].Text = "test";
            loanNote.Code = 50;
            loanNote.CodeSpecified = true;

            var request = new CreateLoanNoteRequest()
            {
                Credentials = _credChoice,
                DeviceInformation = _device,
                MessageId = "CreateLoanNoteRequest",
                BranchIdSpecified = false,
                AccountNumber = accountNumber,
                LoanId = loanID,
                LoanNoteCreatableFields = loanNote
            };

            return await _accountClient.createLoanNoteAsync(request);
        }
    }
}
